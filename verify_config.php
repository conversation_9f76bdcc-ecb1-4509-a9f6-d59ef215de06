<?php
/**
 * Configuration Verification Script
 *
 * This script verifies that the sequential reaction system is properly configured
 */

// Extract configuration from bot.php without executing the webhook logic
$botPhpContent = file_get_contents('bot.php');

// Extract configuration values using regex
preg_match('/\$sequentialChannelId\s*=\s*([^;]+);/', $botPhpContent, $channelMatches);
preg_match('/\$reactionDelayInterval\s*=\s*(\d+);/', $botPhpContent, $delayMatches);
preg_match('/\$maxSequentialBots\s*=\s*(\d+);/', $botPhpContent, $maxBotsMatches);
preg_match('/\$sequentialEmojiPool\s*=\s*\[(.*?)\];/s', $botPhpContent, $emojiMatches);

// Parse extracted values
$sequentialChannelId = isset($channelMatches[1]) ? (trim($channelMatches[1]) === 'null' ? null : (int)trim($channelMatches[1])) : null;
$reactionDelayInterval = isset($delayMatches[1]) ? (int)$delayMatches[1] : 20;
$maxSequentialBots = isset($maxBotsMatches[1]) ? (int)$maxBotsMatches[1] : 44;

// Count bot tokens
preg_match_all('/\'\d+\'\s*=>\s*\'[^\']+\'/', $botPhpContent, $tokenMatches);
$availableBots = count($tokenMatches[0]);

// Parse emoji pool
$sequentialEmojiPool = [];
if (isset($emojiMatches[1])) {
    preg_match_all('/\'([^\']+)\'/', $emojiMatches[1], $emojiList);
    $sequentialEmojiPool = $emojiList[1];
}

echo "=== Sequential Reaction System Configuration Verification ===\n\n";

// Check 1: Sequential Channel ID
echo "1. Sequential Channel Configuration:\n";
echo "   Target Channel ID: ";
if ($sequentialChannelId !== null) {
    echo $sequentialChannelId . " ✓\n";
    echo "   Status: ENABLED\n";
} else {
    echo "NULL ✗\n";
    echo "   Status: DISABLED\n";
    echo "   ERROR: Sequential reactions are disabled!\n";
}
echo "\n";

// Check 2: Emoji Pool
echo "2. Emoji Pool Configuration:\n";
echo "   Emojis: " . implode(' ', $sequentialEmojiPool) . "\n";
echo "   Count: " . count($sequentialEmojiPool) . " emojis ✓\n";
echo "\n";

// Check 3: Timing Configuration
echo "3. Timing Configuration:\n";
echo "   Delay Interval: {$reactionDelayInterval} seconds ✓\n";
echo "   Max Bots: {$maxSequentialBots} bots ✓\n";
echo "   Total Duration: " . (($maxSequentialBots - 1) * $reactionDelayInterval) . " seconds\n";
echo "\n";

// Check 4: Bot Token Availability
echo "4. Bot Token Verification:\n";
echo "   Available Bot Tokens: {$availableBots}\n";
if ($maxSequentialBots <= $availableBots) {
    echo "   Token Coverage: ✓ (Sufficient tokens for {$maxSequentialBots} bots)\n";
} else {
    echo "   Token Coverage: ✗ (Need {$maxSequentialBots} tokens, only have {$availableBots})\n";
}
echo "\n";

// Check 5: File Permissions
echo "5. File System Check:\n";
$queueFile = __DIR__ . '/reaction_queue.json';
$logFile = __DIR__ . '/bot_reactions.log';

echo "   Queue File: ";
if (file_exists($queueFile)) {
    echo "EXISTS ";
    echo (is_readable($queueFile) ? "READABLE " : "NOT_READABLE ");
    echo (is_writable($queueFile) ? "WRITABLE ✓" : "NOT_WRITABLE ✗");
} else {
    echo "NOT_EXISTS (will be created) ✓";
}
echo "\n";

echo "   Log File: ";
if (file_exists($logFile)) {
    echo "EXISTS ";
    echo (is_readable($logFile) ? "READABLE " : "NOT_READABLE ");
    echo (is_writable($logFile) ? "WRITABLE ✓" : "NOT_WRITABLE ✗");
} else {
    echo "NOT_EXISTS (will be created) ✓";
}
echo "\n\n";

// Check 6: Simulate Channel Matching
echo "6. Channel Matching Test:\n";
$testChannelId = -1002888471861; // Your actual channel ID
echo "   Test Channel: {$testChannelId}\n";
echo "   Configured Channel: {$sequentialChannelId}\n";
echo "   Match: ";
if ($sequentialChannelId !== null && $testChannelId == $sequentialChannelId) {
    echo "✓ MATCH - Sequential reactions will activate\n";
} else {
    echo "✗ NO MATCH - Will use legacy behavior\n";
}
echo "\n";

// Check 7: Function Availability
echo "7. Function Availability:\n";
echo "   Functions are defined in bot.php ✓\n";
echo "   (Cannot check function_exists without including bot.php)\n";
echo "\n";

// Summary
echo "=== SUMMARY ===\n";
if ($sequentialChannelId !== null && $sequentialChannelId == -1002888471861) {
    echo "✓ Configuration appears correct for sequential reactions\n";
    echo "✓ Channel ID matches your target channel\n";
    echo "✓ System should activate for new posts in channel {$sequentialChannelId}\n\n";
    
    echo "Expected behavior for new posts:\n";
    echo "1. Only Bot #1 webhook will process the new post\n";
    echo "2. Bot #1 will react immediately\n";
    echo "3. Bots #2-{$maxSequentialBots} will be queued for delayed reactions\n";
    echo "4. Other bots' webhooks will exit early (no legacy reactions)\n";
    echo "5. Queue processor will execute delayed reactions\n\n";
    
    echo "Next steps:\n";
    echo "- Ensure queue processor is running (process_queue.php)\n";
    echo "- Test with a new post in the target channel\n";
    echo "- Monitor bot_reactions.log for SEQUENTIAL entries\n";
} else {
    echo "✗ Configuration issues detected:\n";
    if ($sequentialChannelId === null) {
        echo "- Sequential channel ID is not set (currently null)\n";
        echo "- Set \$sequentialChannelId = -1002888471861; in bot.php\n";
    } elseif ($sequentialChannelId != -1002888471861) {
        echo "- Sequential channel ID mismatch\n";
        echo "- Current: {$sequentialChannelId}\n";
        echo "- Expected: -1002888471861\n";
    }
}

echo "\n=== END VERIFICATION ===\n";
?>
