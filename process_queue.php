<?php
/**
 * Sequential Reaction Queue Processor
 * 
 * This file can be called via cron job or webhook to process delayed reactions.
 * It's designed to work independently of the main bot.php file.
 * 
 * Usage:
 * 1. Via cron job: Set up a cron job to call this file every minute
 * 2. Via webhook: Call this file from an external service every minute
 * 3. Via URL: Access this file directly via browser/curl for manual processing
 */

// ====== CONFIGURATION (must match bot.php) ======

// Map bot IDs (1–44) to your tokens
$botTokens = [
    '1'  => '7324085708:AAECLspc6xqyZZ7lekSsm1ZS6PDhsWRejAE',
    '2'  => '7734048562:AAFPk0NBRHwGyFb-hDtt_Br9i6HlZ5IKd1g',
    '3'  => '8000823167:AAG139icfPTOrXUDbR1lEtYOBsJ2IqzcU7E',
    '4'  => '8121324542:AAFsEWUTlbX4_BcaTE8XAoA2WYO2sxHhwKQ',
    '5'  => '7043095943:AAGhPspmxSlvT1yzT9ARIcT3FFr9pO5FlxI',
    '6'  => '8070640534:AAF0SzNYuqxOQ8DvoIzpgA9LnNjAGWE9gho',
    '7'  => '7414728357:AAFWdAq0faNm2Yczhm0qNAX0-Tqo1u6jrAQ',
    '8'  => '7595655197:AAE-LVH3ao4rRRCeOMHdnqw_hgv7tMxPbiI',
    '9'  => '7631470660:AAGF6R8_udL0tWVqZWTFF43Z8wdYQoRDYSU',
    '10' => '8181651796:AAE2aVmc52tPVBofhzj5RbVvPNZKWQiYL3o',
    '11' => '7807349163:AAGr1S0OS4O09YP-PKxVTXQwh5fgss3VdS4',
    '12' => '7224250149:AAFg8tIbi8xsJHVLZSVv52NHtAfxyJ7p7IA',
    '13' => '7339986151:AAETfpuU3ksUtk5GSkLkPsrb9tGiccSb9dg',
    '14' => '7748840812:AAHDjQVZL1NAkHUMUYGuBqu0sAudlXEc2Uo',
    '15' => '7728027518:AAEBUOi_BPYqqu8qXOvYQfM9GHfVUDwJ0-M',
    '16' => '7601378202:AAHi2Jo0t_W8sEWnxWkDlZqjYAwpUGimG6k',
    '17' => '8005056905:AAGBc7HjP4gvAlrnKrelz94A4mNSgDBqLmg',
    '18' => '5920970906:AAEl59pHfUw0WnqkPnR5VH4HWq35mL_m0u0',
    '19' => '7957888180:AAEsxFpBqlwI1snuXdLxBM9feYo8GrxL71U',
    '20' => '1874727878:AAGhz33r6JB7jf5LA5GmXKuu-NkKE-juO4Y',
    '21' => '1782934509:AAHAvy6rIDj9WOi3N6Waz_MbSbzghrDBsb0',
    '22' => '1854747800:AAFrh45kpaQ7UOMx7rF4YOADrFCh2P3x5Fs',
    '23' => '1812296269:AAE8KpwmmO73Bg9uIxgiihvhz8wd6P-0-ww',
    '24' => '1833448524:AAE1C4jzssdPGA5iYlmrNtWchE6I0E6gURw',
    '25' => '2054730466:AAGaTrFU4UvA2qrt2vmUTBB72G1UFzg7MXk',
    '26' => '2103424776:AAGkaXs8NqiFjfoXJ7G2RPzNMnhpLXCJG98',
    '27' => '5232551378:AAEA6kDOIAUPhl44Eqzo_jTJNV09cHa3Ifg',
    '28' => '5076605250:AAFIwFnhMd16iW27l5SOszE1KVU_X3dLbA4',
    '29' => '5116250996:AAG-a_xoeczvkCr_Z_Ggus9Fu_C49OqgmZ4',
    '30' => '7710186495:AAF46DoNSUQ9PEEDwREx8aOK42Du1rhyXoE',
    '31' => '5989552141:AAGEQ1ObcCmaO68EvwU3XFMRuRHODSAv2o0',
    '32' => '5954755638:AAHyHoMUMmkTgFON6Cwzkq4Pu0bXMVrZKo4',
    '33' => '7638711403:AAFlCDzFZohgrW-duuPiijRMRT-BtKO9a2s',
    '34' => '7992531992:AAHgqBoygZ-vEvGiihwGuaMwG5d3v3BQ-TI',
    '35' => '7680297842:AAGPmB2yufn-n6-xNLIBag5eYEchh6pIF48',
    '36' => '7529351450:AAGFFKB4VadwrZTZjjyqBhuS-bCDMi2fB0s',
    '37' => '7611442057:AAGnhvfJg_EnOITI5mrEhN8p5ehGgiM1ax0',
    '38' => '7496411088:AAF5_Hz4nysgtimtZ0xo1-QcLqFn4dAyEh0',
    '39' => '8171836495:AAFUHd4K7OcuCfnXVqpEoQFBLd4bhxf4maA',
    '40' => '7309785917:AAEDGLAsBw5yEraoPhSO_PfYCkvSyNwGHts',
    '41' => '7464545372:AAGiey-QGPxpfVnBMB3eaMe-MkBV5JVAJus',
    '42' => '7504410338:AAF7rlo9UZAAwiGl6WMwQ8DqQAfQGCTii-o',
    '43' => '7611709153:AAGEjbgr-KZcyDMn7Z5dfQwZZPgLSHssp6Y',
    '44' => '8000444345:AAHicXHhUnRptpI3yp26MB0Af_9rWC9t3QE',
];

// File paths
define('LOGFILE', __DIR__ . '/bot_reactions.log');
define('QUEUE_FILE', __DIR__ . '/reaction_queue.json');

// ====== HELPER FUNCTIONS ======

function logLine($line) {
    file_put_contents(LOGFILE, date('[Y-m-d H:i:s] ') . $line . "\n", FILE_APPEND);
}

function loadQueue() {
    if (!file_exists(QUEUE_FILE)) {
        return [];
    }
    $content = file_get_contents(QUEUE_FILE);
    return $content ? json_decode($content, true) : [];
}

function saveQueue($queue) {
    file_put_contents(QUEUE_FILE, json_encode($queue, JSON_PRETTY_PRINT));
}

function processQueue() {
    global $botTokens;

    $queue = loadQueue();
    $currentTime = time();
    $processedCount = 0;
    $errorCount = 0;
    $remainingQueue = [];

    foreach ($queue as $reaction) {
        if ($reaction['scheduled_time'] <= $currentTime) {
            // Time to execute this reaction
            $botId = $reaction['bot_id'];
            $botToken = $botTokens[(string)$botId] ?? null;

            if ($botToken) {
                $apiURL = "https://api.telegram.org/bot{$botToken}/";
                $emoji = $reaction['emoji'];
                $chatId = $reaction['chat_id'];
                $messageId = $reaction['message_id'];

                // Create URL for the API call
                $url = $apiURL . "setMessageReaction?chat_id={$chatId}&message_id={$messageId}&reaction=[{\"type\":\"emoji\",\"emoji\":\"{$emoji}\"}]&is_big=true";

                // Create context to capture HTTP response details
                $context = stream_context_create([
                    'http' => [
                        'method' => 'GET',
                        'ignore_errors' => true,
                        'timeout' => 10
                    ]
                ]);

                // Execute the reaction with error handling
                $response = file_get_contents($url, false, $context);
                $httpCode = 'Unknown';

                // Extract HTTP response code
                if (isset($http_response_header)) {
                    foreach ($http_response_header as $header) {
                        if (strpos($header, 'HTTP/') === 0) {
                            $parts = explode(' ', $header);
                            $httpCode = isset($parts[1]) ? $parts[1] : 'Unknown';
                            break;
                        }
                    }
                }

                $delay = $currentTime - $reaction['post_time'];

                // Check if the API call was successful
                if ($response !== false && strpos($response, '"ok":true') !== false) {
                    logLine("QUEUE_PROCESSOR: Bot #{$botId} → '{$emoji}' (Message: {$messageId}, Delay: {$delay}s) ✓");
                    $processedCount++;
                } else {
                    // Log the error details
                    $errorMsg = $response ? json_decode($response, true)['description'] ?? 'Unknown error' : 'No response';
                    logLine("QUEUE_PROCESSOR ERROR: Bot #{$botId} → '{$emoji}' (Message: {$messageId}) - HTTP {$httpCode}: {$errorMsg}");
                    $errorCount++;
                }
            } else {
                logLine("QUEUE_PROCESSOR ERROR: Bot token not found for bot #{$botId}");
                $errorCount++;
            }
        } else {
            // Keep this reaction in the queue for later
            $remainingQueue[] = $reaction;
        }
    }

    // Save the updated queue
    saveQueue($remainingQueue);

    if ($processedCount > 0 || $errorCount > 0) {
        logLine("QUEUE_PROCESSOR: Processed {$processedCount} reactions, {$errorCount} errors. " . count($remainingQueue) . " remaining.");
    }

    return $processedCount;
}

// ====== MAIN EXECUTION ======

// Set content type for web access
header('Content-Type: text/plain');

// Process the queue
$startTime = microtime(true);
$processed = processQueue();
$endTime = microtime(true);
$executionTime = round(($endTime - $startTime) * 1000, 2);

// Output results
echo "Queue Processor Results:\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n";
echo "Processed reactions: {$processed}\n";
echo "Execution time: {$executionTime}ms\n";

// If accessed via web, also show queue status
if (isset($_SERVER['HTTP_HOST'])) {
    $queue = loadQueue();
    echo "Remaining in queue: " . count($queue) . "\n";
    
    if (count($queue) > 0) {
        echo "\nNext 5 reactions:\n";
        $next5 = array_slice($queue, 0, 5);
        foreach ($next5 as $reaction) {
            $timeLeft = $reaction['scheduled_time'] - time();
            echo "Bot #{$reaction['bot_id']} → {$reaction['emoji']} in {$timeLeft}s\n";
        }
    }
}

?>
