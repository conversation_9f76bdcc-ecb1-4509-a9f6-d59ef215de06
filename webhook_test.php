<?php
// Simple webhook test - save this as webhook_test.php
error_reporting(E_ALL);
ini_set("display_errors", 1);

$logFile = __DIR__ . "/webhook_debug.log";
$timestamp = date("Y-m-d H:i:s");

// Log all incoming data
$logData = [
    "timestamp" => $timestamp,
    "bot_id" => $_GET["bot_id"] ?? "MISSING",
    "method" => $_SERVER["REQUEST_METHOD"],
    "input" => file_get_contents("php://input"),
    "get_params" => $_GET,
    "post_params" => $_POST
];

file_put_contents($logFile, json_encode($logData, JSON_PRETTY_PRINT) . "\n\n", FILE_APPEND);

echo "Webhook test logged at " . $timestamp;
?>