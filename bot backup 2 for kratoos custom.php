<?php
// ====== CONFIGURATION ======

// Map bot IDs (1–44) to your tokens
$botTokens = [
    '1'  => '7324085708:AAECLspc6xqyZZ7lekSsm1ZS6PDhsWRejAE',
    '2'  => '7734048562:AAFPk0NBRHwGyFb-hDtt_Br9i6HlZ5IKd1g',
    '3'  => '8000823167:AAG139icfPTOrXUDbR1lEtYOBsJ2IqzcU7E',
    '4'  => '8121324542:AAFsEWUTlbX4_BcaTE8XAoA2WYO2sxHhwKQ',
    '5'  => '7043095943:AAGhPspmxSlvT1yzT9ARIcT3FFr9pO5FlxI',
    '6'  => '8070640534:AAF0SzNYuqxOQ8DvoIzpgA9LnNjAGWE9gho',
    '7'  => '7414728357:AAFWdAq0faNm2Yczhm0qNAX0-Tqo1u6jrAQ',
    '8'  => '7595655197:AAE-LVH3ao4rRRCeOMHdnqw_hgv7tMxPbiI',
    '9'  => '7631470660:AAGF6R8_udL0tWVqZWTFF43Z8wdYQoRDYSU',
    '10' => '8181651796:AAE2aVmc52tPVBofhzj5RbVvPNZKWQiYL3o',
    '11' => '7807349163:AAGr1S0OS4O09YP-PKxVTXQwh5fgss3VdS4',
    '12' => '7224250149:AAFg8tIbi8xsJHVLZSVv52NHtAfxyJ7p7IA',
    '13' => '7339986151:AAETfpuU3ksUtk5GSkLkPsrb9tGiccSb9dg',
    '14' => '7748840812:AAHDjQVZL1NAkHUMUYGuBqu0sAudlXEc2Uo',
    '15' => '7728027518:AAEBUOi_BPYqqu8qXOvYQfM9GHfVUDwJ0-M',
    '16' => '7601378202:AAHi2Jo0t_W8sEWnxWkDlZqjYAwpUGimG6k',
    '17' => '8005056905:AAGBc7HjP4gvAlrnKrelz94A4mNSgDBqLmg',
    '18' => '5920970906:AAEl59pHfUw0WnqkPnR5VH4HWq35mL_m0u0',
    '19' => '7957888180:AAEsxFpBqlwI1snuXdLxBM9feYo8GrxL71U',
    '20' => '1874727878:AAGhz33r6JB7jf5LA5GmXKuu-NkKE-juO4Y',
    '21' => '1782934509:AAHAvy6rIDj9WOi3N6Waz_MbSbzghrDBsb0',
    '22' => '1854747800:AAFrh45kpaQ7UOMx7rF4YOADrFCh2P3x5Fs',
    '23' => '1812296269:AAE8KpwmmO73Bg9uIxgiihvhz8wd6P-0-ww',
    '24' => '1833448524:AAE1C4jzssdPGA5iYlmrNtWchE6I0E6gURw',
    '25' => '2054730466:AAGaTrFU4UvA2qrt2vmUTBB72G1UFzg7MXk',
    '26' => '2103424776:AAGkaXs8NqiFjfoXJ7G2RPzNMnhpLXCJG98',
    '27' => '5232551378:AAEA6kDOIAUPhl44Eqzo_jTJNV09cHa3Ifg',
    '28' => '5076605250:AAFIwFnhMd16iW27l5SOszE1KVU_X3dLbA4',
    '29' => '5116250996:AAG-a_xoeczvkCr_Z_Ggus9Fu_C49OqgmZ4',
    '30' => '7710186495:AAF46DoNSUQ9PEEDwREx8aOK42Du1rhyXoE',
    '31' => '5989552141:AAGEQ1ObcCmaO68EvwU3XFMRuRHODSAv2o0',
    '32' => '5954755638:AAHyHoMUMmkTgFON6Cwzkq4Pu0bXMVrZKo4',
    '33' => '7638711403:AAFlCDzFZohgrW-duuPiijRMRT-BtKO9a2s',
    '34' => '7992531992:AAHgqBoygZ-vEvGiihwGuaMwG5d3v3BQ-TI',
    '35' => '7680297842:AAGPmB2yufn-n6-xNLIBag5eYEchh6pIF48',
    '36' => '7529351450:AAGFFKB4VadwrZTZjjyqBhuS-bCDMi2fB0s',
    '37' => '7611442057:AAGnhvfJg_EnOITI5mrEhN8p5ehGgiM1ax0',
    '38' => '7496411088:AAF5_Hz4nysgtimtZ0xo1-QcLqFn4dAyEh0',
    '39' => '8171836495:AAFUHd4K7OcuCfnXVqpEoQFBLd4bhxf4maA',
    '40' => '7309785917:AAEDGLAsBw5yEraoPhSO_PfYCkvSyNwGHts',
    '41' => '7464545372:AAGiey-QGPxpfVnBMB3eaMe-MkBV5JVAJus',
    '42' => '7504410338:AAF7rlo9UZAAwiGl6WMwQ8DqQAfQGCTii-o',
    '43' => '7611709153:AAGEjbgr-KZcyDMn7Z5dfQwZZPgLSHssp6Y',
    '44' => '8000444345:AAHicXHhUnRptpI3yp26MB0Af_9rWC9t3QE',
];

// Fixed reaction blocks
$emojiBlock1 = '❤️';  // bots 1–20
$emojiBlock2 = '👍';  // bots 21–30
$randomPool  = ['🔥','🥰','👏','🎉']; // bots 31–44

// Special channel configuration
$specialChannelId = -1002140916089;
$specialEmojiPool = ['❤️‍🔥', '🐳', '🦄', '💯', '🔥']; // Sequential assignment for special channel using modulo

// Log file path
define('LOGFILE', __DIR__ . '/bot_reactions.log');

// — Logging helper —
function logLine($line) {
    file_put_contents(LOGFILE, date('[Y-m-d H:i:s] ') . $line . "\n", FILE_APPEND);
}

// Read incoming update
$update = json_decode(file_get_contents('php://input'), true);

// Validate bot_id
if (!isset($_GET['bot_id'], $botTokens[$_GET['bot_id']])) {
    http_response_code(400);
    exit("Invalid or missing bot_id");
}
$botId    = intval($_GET['bot_id']);
$botToken = $botTokens[(string)$botId];
$apiURL   = "https://api.telegram.org/bot{$botToken}/";

// Only react to channel_post
if (!isset($update['channel_post'])) {
    exit;
}

// Extract chat_id and message_id first
$chat_id    = $update['channel_post']['chat']['id'];
$message_id = $update['channel_post']['message_id'];

// Determine emoji based on channel
if ($chat_id == $specialChannelId) {
    // Special channel: sequential emoji assignment using modulo logic
    // Bot 1→❤️‍🔥, Bot 2→🐳, Bot 3→🦄, Bot 4→💯, Bot 5→🔥, Bot 6→❤️‍🔥, etc.
    $reaction = $specialEmojiPool[($botId - 1) % 5];
} else {
    // Default behavior for all other channels
    if ($botId >= 1 && $botId <= 20) {
        $reaction = $emojiBlock1;
    } elseif ($botId >= 21 && $botId <= 30) {
        $reaction = $emojiBlock2;
    } else {
        $reaction = $randomPool[array_rand($randomPool)];
    }
}

// Log it
logLine("Bot #{$botId} → '{$reaction}' (Channel: {$chat_id})");

// Send reaction
file_get_contents(
    $apiURL
    . "setMessageReaction?chat_id={$chat_id}"
    . "&message_id={$message_id}"
    . "&reaction=[{\"type\":\"emoji\",\"emoji\":\"{$reaction}\"}]"
    . "&is_big=true"
);
