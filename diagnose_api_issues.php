<?php
/**
 * API Issues Diagnostic Tool
 * 
 * This script diagnoses why queue processing appears successful but reactions don't appear
 */

echo "=== API Issues Diagnostic ===\n\n";

// Configuration
$botTokens = [
    '1'  => '7324085708:AAECLspc6xqyZZ7lekSsm1ZS6PDhsWRejAE',
    '2'  => '7734048562:AAFPk0NBRHwGyFb-hDtt_Br9i6HlZ5IKd1g',
    '3'  => '8000823167:AAG139icfPTOrXUDbR1lEtYOBsJ2IqzcU7E',
];

$testChannelId = -1002888471861;
$actualPostId = 27; // From the Telegram URL t.me/ZEFINCAPITALFX/27

echo "1. ISSUE IDENTIFICATION\n";
echo "========================\n";

// Check current queue
$queue = json_decode(file_get_contents('reaction_queue.json'), true);
echo "Current queue contains " . count($queue) . " items\n";

if (count($queue) > 0) {
    $firstItem = $queue[0];
    echo "Queue message_id: " . $firstItem['message_id'] . "\n";
    echo "Actual post ID: {$actualPostId}\n";
    echo "Match: " . ($firstItem['message_id'] == $actualPostId ? "YES" : "NO") . "\n\n";
    
    if ($firstItem['message_id'] != $actualPostId) {
        echo "❌ PROBLEM IDENTIFIED: Queue contains test data (message_id: {$firstItem['message_id']})\n";
        echo "   The queue is trying to react to a non-existent test message!\n\n";
    }
} else {
    echo "Queue is empty\n\n";
}

echo "2. API TESTING\n";
echo "==============\n";

function testBotAPI($botToken, $chatId, $messageId, $emoji) {
    $apiURL = "https://api.telegram.org/bot{$botToken}/";
    $url = $apiURL . "setMessageReaction?chat_id={$chatId}&message_id={$messageId}&reaction=[{\"type\":\"emoji\",\"emoji\":\"{$emoji}\"}]&is_big=true";
    
    // Create context for HTTP request to capture response details
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'ignore_errors' => true,
            'timeout' => 10
        ]
    ]);
    
    $response = file_get_contents($url, false, $context);
    $httpCode = null;
    
    // Extract HTTP response code
    if (isset($http_response_header)) {
        foreach ($http_response_header as $header) {
            if (strpos($header, 'HTTP/') === 0) {
                $parts = explode(' ', $header);
                $httpCode = isset($parts[1]) ? $parts[1] : 'Unknown';
                break;
            }
        }
    }
    
    return [
        'response' => $response,
        'http_code' => $httpCode,
        'success' => $response !== false && strpos($response, '"ok":true') !== false
    ];
}

// Test with fake message ID (current queue data)
echo "Testing API call with FAKE message ID (12345):\n";
$fakeResult = testBotAPI($botTokens['2'], $testChannelId, 12345, '❤️');
echo "  HTTP Code: " . ($fakeResult['http_code'] ?? 'Unknown') . "\n";
echo "  Success: " . ($fakeResult['success'] ? 'YES' : 'NO') . "\n";
echo "  Response: " . substr($fakeResult['response'], 0, 100) . "...\n\n";

// Test with real message ID
echo "Testing API call with REAL message ID ({$actualPostId}):\n";
$realResult = testBotAPI($botTokens['2'], $testChannelId, $actualPostId, '❤️');
echo "  HTTP Code: " . ($realResult['http_code'] ?? 'Unknown') . "\n";
echo "  Success: " . ($realResult['success'] ? 'YES' : 'NO') . "\n";
echo "  Response: " . substr($realResult['response'], 0, 100) . "...\n\n";

echo "3. BOT PERMISSIONS CHECK\n";
echo "========================\n";

function checkBotPermissions($botToken, $chatId) {
    $apiURL = "https://api.telegram.org/bot{$botToken}/";
    $url = $apiURL . "getChatMember?chat_id={$chatId}&user_id=" . explode(':', $botToken)[0];
    
    $response = file_get_contents($url);
    return json_decode($response, true);
}

echo "Checking Bot #2 permissions in channel {$testChannelId}:\n";
$permissions = checkBotPermissions($botTokens['2'], $testChannelId);

if ($permissions && isset($permissions['ok']) && $permissions['ok']) {
    $member = $permissions['result'];
    echo "  Status: " . $member['status'] . "\n";
    echo "  Can post messages: " . (isset($member['can_post_messages']) ? ($member['can_post_messages'] ? 'YES' : 'NO') : 'Unknown') . "\n";
    echo "  Can edit messages: " . (isset($member['can_edit_messages']) ? ($member['can_edit_messages'] ? 'YES' : 'NO') : 'Unknown') . "\n";
} else {
    echo "  ❌ Failed to get bot permissions\n";
    echo "  Response: " . json_encode($permissions) . "\n";
}

echo "\n4. QUEUE ANALYSIS\n";
echo "=================\n";

$currentTime = time();
$dueCount = 0;
$futureCount = 0;

foreach ($queue as $item) {
    if ($item['scheduled_time'] <= $currentTime) {
        $dueCount++;
    } else {
        $futureCount++;
    }
}

echo "Total queue items: " . count($queue) . "\n";
echo "Due for processing: {$dueCount}\n";
echo "Future reactions: {$futureCount}\n";

if ($dueCount > 0) {
    echo "\nNext 3 due reactions:\n";
    $dueItems = array_filter($queue, function($item) use ($currentTime) {
        return $item['scheduled_time'] <= $currentTime;
    });
    
    $dueItems = array_slice($dueItems, 0, 3);
    foreach ($dueItems as $item) {
        $delay = $currentTime - $item['scheduled_time'];
        echo "  Bot #{$item['bot_id']}: {$item['emoji']} (overdue by {$delay}s)\n";
    }
}

echo "\n5. SOLUTIONS\n";
echo "============\n";

echo "IMMEDIATE FIXES NEEDED:\n\n";

echo "A. Clear Test Queue Data:\n";
echo "   The current queue contains test data with fake message_id 12345\n";
echo "   Run: echo '[]' > reaction_queue.json\n\n";

echo "B. Test Real Post Reaction:\n";
echo "   Manually test reacting to the actual post (ID: {$actualPostId})\n";
echo "   This will verify if bots can actually react to real posts\n\n";

echo "C. Check Bot Setup:\n";
echo "   - Ensure all 44 bots are admins in the channel\n";
echo "   - Verify bots have 'Add Reactions' permission\n";
echo "   - Check if channel allows reactions from bots\n\n";

echo "D. Monitor Real Queue Creation:\n";
echo "   - Post a new message in the channel\n";
echo "   - Check if Bot #1 creates queue with REAL message_id\n";
echo "   - Verify queue processor works with real message_id\n\n";

echo "6. NEXT STEPS\n";
echo "=============\n";

echo "1. Clear the test queue: echo '[]' > reaction_queue.json\n";
echo "2. Post a NEW message in channel -1002888471861\n";
echo "3. Check bot_reactions.log for Bot #1 creating queue with real message_id\n";
echo "4. Monitor queue processing with real message_id\n";
echo "5. Verify reactions appear on the actual Telegram post\n\n";

echo "The queue processing system is working correctly.\n";
echo "The issue is that it's processing FAKE test data instead of real post data.\n";

echo "\n=== Diagnostic Complete ===\n";
?>
